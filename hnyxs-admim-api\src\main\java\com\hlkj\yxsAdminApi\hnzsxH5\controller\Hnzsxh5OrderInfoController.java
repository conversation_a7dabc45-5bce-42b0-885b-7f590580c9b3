package com.hlkj.yxsAdminApi.hnzsxH5.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;
import com.hlkj.yxsAdminApi.common.core.utils.InterfaceUtil;
import com.hlkj.yxsAdminApi.common.core.utils.RedisUtil;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.system.entity.R;
import com.hlkj.yxsAdminApi.common.system.entity.User;
import com.hlkj.yxsAdminApi.common.system.service.*;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.*;
import com.hlkj.yxsAdminApi.hnzsx.entity.HnzsxUser;
import com.hlkj.yxsAdminApi.hnzsx.service.HnzsxUserService;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderCountParam;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderDailyParam;
import com.hlkj.yxsAdminApi.hnzsxH5.service.*;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderInfoParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import com.alibaba.excel.EasyExcel;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * H5即时受理-订单基础信息控制器
 *
 * <AUTHOR>
 * @since 2025-04-15 17:54:29
 */
@Api(tags = "H5即时受理-订单基础信息管理")
@RestController
@RequestMapping("/api/hnzsxH5/hnzsxh5-order-info")
public class Hnzsxh5OrderInfoController extends BaseController {
    @Autowired
    private Hnzsxh5OrderInfoService hnzsxh5OrderInfoService;
    @Autowired
    private Hnzsxh5OrderInfoMktinstlistService hnzsxh5OrderInfoMktinstlistService;
    @Autowired
    private Hnzsxh5OrderInfoMktinstlistAttrlistService hnzsxh5OrderInfoMktinstlistAttrlistService;
    @Autowired
    private Hnzsxh5OrderInfoFuncinstlistService hnzsxh5OrderInfoFuncinstlistService;
    @Autowired
    private Hnzsxh5OrderInfoFuncinstlistAttrlistService hnzsxh5OrderInfoFuncinstlistAttrlistService;
    @Autowired
    private Hnzsxh5OrderInfoProdinstlistService hnzsxh5OrderInfoProdinstlistService;
    @Autowired
    private Hnzsxh5OrderInfoProdinstlistAttrlistService hnzsxh5OrderInfoProdinstlistAttrlistService;
    @Autowired
    private Hnzsxh5OrderInfoOfferinstlistService hnzsxh5OrderInfoOfferinstlistService;
    @Autowired
    private Hnzsxh5OrderInfoOfferinstlistAttrlistService hnzsxh5OrderInfoOfferinstlistAttrlistService;
    @Autowired
    private Hnzsxh5OrderInfoOrderattrlistService hnzsxh5OrderInfoOrderattrlistService;
    @Autowired
    private UserService userService;
    @Resource
    private RedisUtil redisUtil;


    //@PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5OrderInfo:list')")
    @OperationLog(value ="分页查询H5即时受理-订单基础信息",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/订单管理")
    @ApiOperation("分页查询H5即时受理-订单基础信息")
    @PostMapping("/page")
    public ApiResult<PageResult<Hnzsxh5OrderInfo>> page(@RequestBody Hnzsxh5OrderInfoParam param) {
        param.setSort("createdDate");
        param.setOrder("desc");
        User user = getLoginUser();
        if (redisUtil.limitRateCount(Constants.limitUser, user)) {
            // 熔断账号冻结
            user.setStatus(1);
            userService.updateById(user);
            // 推送熔断日志用户信息
            InterfaceUtil.breakerLog(user, "分页查询H5即时受理-订单基础信息");
            return new ApiResult<>(Constants.RESULT_ERROR_CODE, "当前查询次数已被限制");
        }
        PageParam<Hnzsxh5OrderInfo, Hnzsxh5OrderInfoParam> page = new PageParam<>(param);
        ApiResult<PageResult<Hnzsxh5OrderInfo>> resultApiResult = success(hnzsxh5OrderInfoService.page(page, page.getWrapper()));
        
        // 执行敏感信息脱敏处理
        if (resultApiResult != null && resultApiResult.getData() != null) {
            List<Hnzsxh5OrderInfo> orderList = resultApiResult.getData().getList();
            if (orderList != null && !orderList.isEmpty()) {
                for (Hnzsxh5OrderInfo orderInfo : orderList) {
                    handleOrderInfoDesensitization(orderInfo);
                }
            }
        }
        
        return resultApiResult;
    }


    @ApiOperation("分页查询H5即时受理-订单统计")
    @PostMapping("/getCount")
    public ApiResult<List<Hnzsxh5OrderCountParam>> getOrderCount(@RequestBody Hnzsxh5OrderCountParam param) {
        return success(hnzsxh5OrderInfoService.getGoodsSalesByDate(param.getDate(), param.getEndDate()));
    }


    @ApiOperation("分页查询H5即时受理-订单统计")
    @PostMapping("/getCountByGoodId")
    public ApiResult<List<Hnzsxh5OrderCountParam>> getCountByGoodId(@RequestBody Hnzsxh5OrderCountParam param) {
        return success(hnzsxh5OrderInfoService.getCountByGoodId(param.getDate(), param.getEndDate()));
    }

    @OperationLog(value ="分页查询H5即时受理-获取各地市每日订单数量",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/订单管理")
    @ApiOperation("获取各地市每日订单数量")
    @PostMapping("/getDailyOrdersByCityCode")
    public ApiResult<List<Hnzsxh5OrderDailyParam>> getDailyOrdersByCityCode(@RequestBody Hnzsxh5OrderCountParam param) {
        return success(hnzsxh5OrderInfoService.getDailyOrdersByCityCode(param.getDate(), param.getEndDate()));
    }

    @OperationLog(value ="分页查询H5即时受理-获取各地市按模块分类的订单数量",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/订单管理")
    @ApiOperation("获取各地市按模块分类的订单数量")
    @PostMapping("/getOrderCountByModule")
    public ApiResult<Map<String, Object>> getOrderCountByModule(@RequestBody Hnzsxh5OrderCountParam param) {
        return success(hnzsxh5OrderInfoService.getOrderCountByModule(param.getDate(), param.getEndDate()));
    }

    @OperationLog(value ="分页查询H5即时受理-获取按模块类型汇总发展总量统计",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/订单管理")
    @ApiOperation("获取按模块类型汇总发展总量统计")
    @PostMapping("/getOrderCountByModuleType")
    public ApiResult<List<Map<String, Object>>> getOrderCountByModuleType(@RequestBody Hnzsxh5OrderCountParam param) {
        return success(hnzsxh5OrderInfoService.getOrderCountByModuleType(param.getDate(), param.getEndDate()));
    }

    //@PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5OrderInfo:list')")
    @OperationLog
    @ApiOperation("查询全部H5即时受理-订单基础信息")
    @PostMapping("/getAllListByParam")
    public ApiResult<List<Hnzsxh5OrderInfo>> list(@RequestBody Hnzsxh5OrderInfoParam param) {
        PageParam<Hnzsxh5OrderInfo, Hnzsxh5OrderInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<Hnzsxh5OrderInfo> orderList = hnzsxh5OrderInfoService.list(page.getOrderWrapper());
        
        // 使用关联查询
        //return success(hnzsxh5OrderInfoService.listRel(param));
        for (Hnzsxh5OrderInfo hnzsxh5OrderInfo : orderList) {
            this.getOrderExtInfo(hnzsxh5OrderInfo);
            // 执行敏感信息脱敏处理
            handleOrderInfoDesensitization(hnzsxh5OrderInfo);
        }
        return success(orderList);
    }

    //@PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5OrderInfo:list')")
    @OperationLog(value ="分页查询H5即时受理-根据id查询H5即时受理",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "分页查询H5即时受理/订单管理")
    @ApiOperation("根据id查询H5即时受理-订单基础信息")
    @PostMapping("/getInfoById")
    public ApiResult<?> get(@RequestBody Hnzsxh5OrderInfoParam param) {
        if (param.getId() == null) {
            return fail("必输项不能为空");
        } else {
            int id = param.getId();
            Hnzsxh5OrderInfo hnzsxh5OrderInfo = hnzsxh5OrderInfoService.getById(id);
            if (hnzsxh5OrderInfo == null) {
                return fail("根据订单号未查询到信息");
            }
            this.getOrderExtInfo(hnzsxh5OrderInfo);
            // 执行敏感信息脱敏处理
            handleOrderInfoDesensitization(hnzsxh5OrderInfo);
            return success(hnzsxh5OrderInfo);
        }
        // 使用关联查询
        //return success(hnzsxh5OrderInfoService.getByIdRel(id));
    }
    
    /**
     * 对订单信息中的敏感数据进行脱敏处理
     *
     * @param orderInfo 订单信息对象
     */
    private void handleOrderInfoDesensitization(Hnzsxh5OrderInfo orderInfo) {
        if (orderInfo == null) {
            return;
        }
        
        // 对订单基本信息中的敏感字段进行脱敏
        orderInfo.setUserName(DesensitizationUtil.desensitizedName(orderInfo.getUserName()));
        orderInfo.setUserMoble(DesensitizationUtil.mobilePhoneDesensitization(orderInfo.getUserMoble()));
        orderInfo.setUserCard(DesensitizationUtil.idCardDesensitization(orderInfo.getUserCard()));
        orderInfo.setUserAddress(DesensitizationUtil.addressDesensitization(orderInfo.getUserAddress()));
        orderInfo.setLoginUserName(DesensitizationUtil.desensitizedName(orderInfo.getLoginUserName()));
        orderInfo.setLoginUserPhone(DesensitizationUtil.mobilePhoneDesensitization(orderInfo.getLoginUserPhone()));
        
        if (StringUtils.isNotEmpty(orderInfo.getReceptInvoiceEmail())) {
            orderInfo.setReceptInvoiceEmail(DesensitizationUtil.emailDesensitization(orderInfo.getReceptInvoiceEmail()));
        }
        
        // 对订单相关子表中可能存在的敏感信息进行脱敏处理
        // 处理终端列表数据
        if (orderInfo.getHnzsxh5OrderInfoMktinstlist() != null) {
            for (Hnzsxh5OrderInfoMktinstlist mktinstlist : orderInfo.getHnzsxh5OrderInfoMktinstlist()) {
                if (mktinstlist.getHnzsxh5OrderInfoMktinstlistAttrlist() != null) {
                    for (Hnzsxh5OrderInfoMktinstlistAttrlist attrlist : mktinstlist.getHnzsxh5OrderInfoMktinstlistAttrlist()) {
                        desensitizeAttribute(attrlist.getAttrId(), attrlist);
                    }
                }
            }
        }
        
        // 处理AI行业服务列表
        if (orderInfo.getHnzsxh5OrderInfoFuncinstlist() != null) {
            for (Hnzsxh5OrderInfoFuncinstlist funcinstlist : orderInfo.getHnzsxh5OrderInfoFuncinstlist()) {
                if (funcinstlist.getHnzsxh5OrderInfoFuncinstlistAttrlists() != null) {
                    for (Hnzsxh5OrderInfoFuncinstlistAttrlist attrlist : funcinstlist.getHnzsxh5OrderInfoFuncinstlistAttrlists()) {
                        desensitizeAttribute(attrlist.getAttrId(), attrlist);
                    }
                }
            }
        }
        
        // 处理产品列表
        if (orderInfo.getHnzsxh5OrderInfoProdinstlists() != null) {
            for (Hnzsxh5OrderInfoProdinstlist prodinstlist : orderInfo.getHnzsxh5OrderInfoProdinstlists()) {
                if (prodinstlist.getHnzsxh5OrderInfoProdinstlistAttrlists() != null) {
                    for (Hnzsxh5OrderInfoProdinstlistAttrlist attrlist : prodinstlist.getHnzsxh5OrderInfoProdinstlistAttrlists()) {
                        desensitizeAttribute(attrlist.getAttrId(), attrlist);
                    }
                }
            }
        }
        
        // 处理资费列表
        if (orderInfo.getHnzsxh5OrderInfoOfferinstlists() != null) {
            for (Hnzsxh5OrderInfoOfferinstlist offerinstlist : orderInfo.getHnzsxh5OrderInfoOfferinstlists()) {
                if (offerinstlist.getHnzsxh5OrderInfoOfferinstlistAttrlists() != null) {
                    for (Hnzsxh5OrderInfoOfferinstlistAttrlist attrlist : offerinstlist.getHnzsxh5OrderInfoOfferinstlistAttrlists()) {
                        desensitizeAttribute(attrlist.getAttrId(), attrlist);
                    }
                }
            }
        }
        
        // 处理订单详细属性列表
        if (orderInfo.getHnzsxh5OrderInfoOrderattrlists() != null) {
            for (Hnzsxh5OrderInfoOrderattrlist orderattrlist : orderInfo.getHnzsxh5OrderInfoOrderattrlists()) {
                desensitizeOrderAttribute(orderattrlist.getAttrId(), orderattrlist);
            }
        }
    }
    
    /**
     * 根据属性ID对属性列表中的敏感属性值进行脱敏
     * 
     * @param attrId 属性ID
     * @param attrObject 需要处理的属性对象
     */
    private void desensitizeAttribute(String attrId, Object attrObject) {
        // 根据业务需求，针对不同的属性ID进行脱敏处理
        // 这里需要根据具体的属性ID和业务需求进行补充
        if (attrObject instanceof Hnzsxh5OrderInfoMktinstlistAttrlist) {
            Hnzsxh5OrderInfoMktinstlistAttrlist attr = (Hnzsxh5OrderInfoMktinstlistAttrlist) attrObject;
            // 例如：手机号码、姓名、身份证、地址等敏感属性
            if ("CUSTOMER_NAME".equals(attrId) || "USER_NAME".equals(attrId) || attrId != null && attrId.contains("NAME")) {
                attr.setAttrValue(DesensitizationUtil.desensitizedName(attr.getAttrValue()));
            } else if ("MSISDN".equals(attrId) || "CONTACT_NUMBER".equals(attrId) || "PHONE".equals(attrId) || attrId != null && attrId.contains("PHONE")) {
                attr.setAttrValue(DesensitizationUtil.mobilePhoneDesensitization(attr.getAttrValue()));
            } else if ("CUST_CERT_NO".equals(attrId) || "ID_CARD".equals(attrId) || attrId != null && attrId.contains("CERT")) {
                attr.setAttrValue(DesensitizationUtil.idCardDesensitization(attr.getAttrValue()));
            } else if ("ADDRESS".equals(attrId) || attrId != null && attrId.contains("ADDR")) {
                attr.setAttrValue(DesensitizationUtil.addressDesensitization(attr.getAttrValue()));
            } else if ("EMAIL".equals(attrId) || attrId != null && attrId.contains("MAIL")) {
                attr.setAttrValue(DesensitizationUtil.emailDesensitization(attr.getAttrValue()));
            }
        } else if (attrObject instanceof Hnzsxh5OrderInfoProdinstlistAttrlist) {
            Hnzsxh5OrderInfoProdinstlistAttrlist attr = (Hnzsxh5OrderInfoProdinstlistAttrlist) attrObject;
            if ("CUSTOMER_NAME".equals(attrId) || "USER_NAME".equals(attrId) || attrId != null && attrId.contains("NAME")) {
                attr.setAttrValue(DesensitizationUtil.desensitizedName(attr.getAttrValue()));
            } else if ("MSISDN".equals(attrId) || "CONTACT_NUMBER".equals(attrId) || "PHONE".equals(attrId) || attrId != null && attrId.contains("PHONE")) {
                attr.setAttrValue(DesensitizationUtil.mobilePhoneDesensitization(attr.getAttrValue()));
            } else if ("CUST_CERT_NO".equals(attrId) || "ID_CARD".equals(attrId) || attrId != null && attrId.contains("CERT")) {
                attr.setAttrValue(DesensitizationUtil.idCardDesensitization(attr.getAttrValue()));
            } else if ("ADDRESS".equals(attrId) || attrId != null && attrId.contains("ADDR")) {
                attr.setAttrValue(DesensitizationUtil.addressDesensitization(attr.getAttrValue()));
            } else if ("EMAIL".equals(attrId) || attrId != null && attrId.contains("MAIL")) {
                attr.setAttrValue(DesensitizationUtil.emailDesensitization(attr.getAttrValue()));
            }
        } else if (attrObject instanceof Hnzsxh5OrderInfoFuncinstlistAttrlist) {
            Hnzsxh5OrderInfoFuncinstlistAttrlist attr = (Hnzsxh5OrderInfoFuncinstlistAttrlist) attrObject;
            if ("CUSTOMER_NAME".equals(attrId) || "USER_NAME".equals(attrId) || attrId != null && attrId.contains("NAME")) {
                attr.setAttrValue(DesensitizationUtil.desensitizedName(attr.getAttrValue()));
            } else if ("MSISDN".equals(attrId) || "CONTACT_NUMBER".equals(attrId) || "PHONE".equals(attrId) || attrId != null && attrId.contains("PHONE")) {
                attr.setAttrValue(DesensitizationUtil.mobilePhoneDesensitization(attr.getAttrValue()));
            } else if ("CUST_CERT_NO".equals(attrId) || "ID_CARD".equals(attrId) || attrId != null && attrId.contains("CERT")) {
                attr.setAttrValue(DesensitizationUtil.idCardDesensitization(attr.getAttrValue()));
            } else if ("ADDRESS".equals(attrId) || attrId != null && attrId.contains("ADDR")) {
                attr.setAttrValue(DesensitizationUtil.addressDesensitization(attr.getAttrValue()));
            } else if ("EMAIL".equals(attrId) || attrId != null && attrId.contains("MAIL")) {
                attr.setAttrValue(DesensitizationUtil.emailDesensitization(attr.getAttrValue()));
            }
        } else if (attrObject instanceof Hnzsxh5OrderInfoOfferinstlistAttrlist) {
            Hnzsxh5OrderInfoOfferinstlistAttrlist attr = (Hnzsxh5OrderInfoOfferinstlistAttrlist) attrObject;
            if ("CUSTOMER_NAME".equals(attrId) || "USER_NAME".equals(attrId) || attrId != null && attrId.contains("NAME")) {
                attr.setAttrValue(DesensitizationUtil.desensitizedName(attr.getAttrValue()));
            } else if ("MSISDN".equals(attrId) || "CONTACT_NUMBER".equals(attrId) || "PHONE".equals(attrId) || attrId != null && attrId.contains("PHONE")) {
                attr.setAttrValue(DesensitizationUtil.mobilePhoneDesensitization(attr.getAttrValue()));
            } else if ("CUST_CERT_NO".equals(attrId) || "ID_CARD".equals(attrId) || attrId != null && attrId.contains("CERT")) {
                attr.setAttrValue(DesensitizationUtil.idCardDesensitization(attr.getAttrValue()));
            } else if ("ADDRESS".equals(attrId) || attrId != null && attrId.contains("ADDR")) {
                attr.setAttrValue(DesensitizationUtil.addressDesensitization(attr.getAttrValue()));
            } else if ("EMAIL".equals(attrId) || attrId != null && attrId.contains("MAIL")) {
                attr.setAttrValue(DesensitizationUtil.emailDesensitization(attr.getAttrValue()));
            }
        }
    }
    
    /**
     * 对订单属性表中的敏感属性值进行脱敏
     * 
     * @param attrId 属性ID
     * @param attr 订单属性对象
     */
    private void desensitizeOrderAttribute(String attrId, Hnzsxh5OrderInfoOrderattrlist attr) {
        // 根据业务需求，针对不同的属性ID进行脱敏处理
        if ("CUSTOMER_NAME".equals(attrId) || "USER_NAME".equals(attrId) || attrId != null && attrId.contains("NAME")) {
            attr.setAttrValue(DesensitizationUtil.desensitizedName(attr.getAttrValue()));
        } else if ("MSISDN".equals(attrId) || "CONTACT_NUMBER".equals(attrId) || "PHONE".equals(attrId) || attrId != null && attrId.contains("PHONE")) {
            attr.setAttrValue(DesensitizationUtil.mobilePhoneDesensitization(attr.getAttrValue()));
        } else if ("CUST_CERT_NO".equals(attrId) || "ID_CARD".equals(attrId) || attrId != null && attrId.contains("CERT")) {
            attr.setAttrValue(DesensitizationUtil.idCardDesensitization(attr.getAttrValue()));
        } else if ("ADDRESS".equals(attrId) || attrId != null && attrId.contains("ADDR")) {
            attr.setAttrValue(DesensitizationUtil.addressDesensitization(attr.getAttrValue()));
        } else if ("EMAIL".equals(attrId) || attrId != null && attrId.contains("MAIL")) {
            attr.setAttrValue(DesensitizationUtil.emailDesensitization(attr.getAttrValue()));
        }
    }

    private void getOrderExtInfo(Hnzsxh5OrderInfo hnzsxh5OrderInfo) {
        String orderNo = hnzsxh5OrderInfo.getOrderNo();
        //获取Hnzsxh5OrderInfoMktinstlist相关
        LambdaQueryWrapper<Hnzsxh5OrderInfoMktinstlist> OrderInfoMktinstlistQueryWrapper = Wrappers.lambdaQuery();
        OrderInfoMktinstlistQueryWrapper.eq(Hnzsxh5OrderInfoMktinstlist::getOrderNo, orderNo);
        //根据orderNo查询hnzsxh5_order_info_mktinstlist
        List<Hnzsxh5OrderInfoMktinstlist> hnzsxh5OrderInfoMktinstlist = hnzsxh5OrderInfoMktinstlistService.list(OrderInfoMktinstlistQueryWrapper);
        //设置hnzsxh5_order_info_mktinstlist对象
        hnzsxh5OrderInfo.setHnzsxh5OrderInfoMktinstlist(hnzsxh5OrderInfoMktinstlist);
        for (Hnzsxh5OrderInfoMktinstlist hnzsxh5OrderInfoMktinstlist1 : hnzsxh5OrderInfoMktinstlist) {
            String mktinsrId = hnzsxh5OrderInfoMktinstlist1.getId();
            LambdaQueryWrapper<Hnzsxh5OrderInfoMktinstlistAttrlist> orderInfoMktinstlistAttrlistQueryWrapper = Wrappers.lambdaQuery();
            orderInfoMktinstlistAttrlistQueryWrapper.eq(Hnzsxh5OrderInfoMktinstlistAttrlist::getOrderInfoMktinstlistId, mktinsrId);
            //根据ORDER_INFO_MKTINSTLIST_ID查询hnzsxh5_order_info_mktinstlist_attrlist表并设值
            List<Hnzsxh5OrderInfoMktinstlistAttrlist> hnzsxh5OrderInfoMktinstlistAttrlist = hnzsxh5OrderInfoMktinstlistAttrlistService.list(orderInfoMktinstlistAttrlistQueryWrapper);
            hnzsxh5OrderInfoMktinstlist1.setHnzsxh5OrderInfoMktinstlistAttrlist(hnzsxh5OrderInfoMktinstlistAttrlist);
        }

        //获取Hnzsxh5OrderInfoFuncinstlist相关开始
        LambdaQueryWrapper<Hnzsxh5OrderInfoFuncinstlist> hnzsxh5OrderInfoFuncinstlistLambdaQueryWrapper = Wrappers.lambdaQuery();
        hnzsxh5OrderInfoFuncinstlistLambdaQueryWrapper.eq(Hnzsxh5OrderInfoFuncinstlist::getOrderNo, orderNo);
        //根据orderNo查询Hnzsxh5OrderInfoFuncinstlist
        List<Hnzsxh5OrderInfoFuncinstlist> hnzsxh5OrderInfoFuncinstlist = hnzsxh5OrderInfoFuncinstlistService.list(hnzsxh5OrderInfoFuncinstlistLambdaQueryWrapper);
        //设置Hnzsxh5OrderInfoFuncinstlist对象
        hnzsxh5OrderInfo.setHnzsxh5OrderInfoFuncinstlist(hnzsxh5OrderInfoFuncinstlist);
        for (Hnzsxh5OrderInfoFuncinstlist hnzsxh5OrderInfoFuncinstlist1 : hnzsxh5OrderInfoFuncinstlist) {
            String funcinstlist1Id = hnzsxh5OrderInfoFuncinstlist1.getId();
            LambdaQueryWrapper<Hnzsxh5OrderInfoFuncinstlistAttrlist> hnzsxh5OrderInfoFuncinstlistAttrlistQueryWrapper = Wrappers.lambdaQuery();
            hnzsxh5OrderInfoFuncinstlistAttrlistQueryWrapper.eq(Hnzsxh5OrderInfoFuncinstlistAttrlist::getOrderInfoFuncinstlistId, funcinstlist1Id);
            //根据ORDER_INFO_MKTINSTLIST_ID查询hnzsxh5_order_info_mktinstlist_attrlist表并设值
            List<Hnzsxh5OrderInfoFuncinstlistAttrlist> hnzsxh5OrderInfoMktinstlistAttrlist = hnzsxh5OrderInfoFuncinstlistAttrlistService.list(hnzsxh5OrderInfoFuncinstlistAttrlistQueryWrapper);
            hnzsxh5OrderInfoFuncinstlist1.setHnzsxh5OrderInfoFuncinstlistAttrlists(hnzsxh5OrderInfoMktinstlistAttrlist);
        }

        //获取Hnzsxh5OrderInfoProdinstlist相关开始
        LambdaQueryWrapper<Hnzsxh5OrderInfoProdinstlist> hnzsxh5OrderInfoProdinstlistLambdaQueryWrapper = Wrappers.lambdaQuery();
        hnzsxh5OrderInfoProdinstlistLambdaQueryWrapper.eq(Hnzsxh5OrderInfoProdinstlist::getOrderNo, orderNo);
        //根据orderNo查询hnzsxh5_order_info_prodinstlist
        List<Hnzsxh5OrderInfoProdinstlist> hnzsxh5OrderInfoProdinstlists = hnzsxh5OrderInfoProdinstlistService.list(hnzsxh5OrderInfoProdinstlistLambdaQueryWrapper);
        //设置hnzsxh5_order_info_prodinstlistt对象
        hnzsxh5OrderInfo.setHnzsxh5OrderInfoProdinstlists(hnzsxh5OrderInfoProdinstlists);
        for (Hnzsxh5OrderInfoProdinstlist hnzsxh5OrderInfoProdinstlist : hnzsxh5OrderInfoProdinstlists) {
            String prodinstId = hnzsxh5OrderInfoProdinstlist.getId();
            LambdaQueryWrapper<Hnzsxh5OrderInfoProdinstlistAttrlist> hnzsxh5OrderInfoProdinstlistAttrlistLambdaQueryWrapper = Wrappers.lambdaQuery();
            hnzsxh5OrderInfoProdinstlistAttrlistLambdaQueryWrapper.eq(Hnzsxh5OrderInfoProdinstlistAttrlist::getOrderInfoProdinstlistId, prodinstId);
            //根据ORDER_INFO_MKTINSTLIST_ID查询hnzsxh5_order_info_prodinstlist_attrlist表并设值
            List<Hnzsxh5OrderInfoProdinstlistAttrlist> hnzsxh5OrderInfoMktinstlistAttrlist = hnzsxh5OrderInfoProdinstlistAttrlistService.list(hnzsxh5OrderInfoProdinstlistAttrlistLambdaQueryWrapper);
            hnzsxh5OrderInfoProdinstlist.setHnzsxh5OrderInfoProdinstlistAttrlists(hnzsxh5OrderInfoMktinstlistAttrlist);
        }

        //获取hnzsxh5_order_info_offerinstlist相关开始
        LambdaQueryWrapper<Hnzsxh5OrderInfoOfferinstlist> hnzsxh5OrderInfoOfferinstlistQueryWrapper = Wrappers.lambdaQuery();
        hnzsxh5OrderInfoOfferinstlistQueryWrapper.eq(Hnzsxh5OrderInfoOfferinstlist::getOrderNo, orderNo);
        //根据orderNo查询hnzsxh5_order_info_offerinstlist
        List<Hnzsxh5OrderInfoOfferinstlist> hnzsxh5OrderInfoOfferinstlists = hnzsxh5OrderInfoOfferinstlistService.list(hnzsxh5OrderInfoOfferinstlistQueryWrapper);
        //设置hnzsxh5_order_info_offerinstlist对象
        hnzsxh5OrderInfo.setHnzsxh5OrderInfoOfferinstlists(hnzsxh5OrderInfoOfferinstlists);
        for (Hnzsxh5OrderInfoOfferinstlist hnzsxh5OrderInfoOfferinstlist : hnzsxh5OrderInfoOfferinstlists) {
            String offerinstlistId = hnzsxh5OrderInfoOfferinstlist.getId();
            LambdaQueryWrapper<Hnzsxh5OrderInfoOfferinstlistAttrlist> hnzsxh5OrderInfoOfferinstlistAttrlistLambdaQueryWrapper = Wrappers.lambdaQuery();
            hnzsxh5OrderInfoOfferinstlistAttrlistLambdaQueryWrapper.eq(Hnzsxh5OrderInfoOfferinstlistAttrlist::getOrderInfoOfferinstlistId, offerinstlistId);
            //根据offerinstlistId查询hnzsxh5_order_info_offerinstlist_attrlist表并设值
            List<Hnzsxh5OrderInfoOfferinstlistAttrlist> hnzsxh5OrderInfoOfferinstlistAttrlists = hnzsxh5OrderInfoOfferinstlistAttrlistService.list(hnzsxh5OrderInfoOfferinstlistAttrlistLambdaQueryWrapper);
            hnzsxh5OrderInfoOfferinstlist.setHnzsxh5OrderInfoOfferinstlistAttrlists(hnzsxh5OrderInfoOfferinstlistAttrlists);
        }

        //获取hnzsxh5_order_info_orderattrlist相关开始
        LambdaQueryWrapper<Hnzsxh5OrderInfoOrderattrlist> hnzsxh5OrderInfoOrderattrlistLambdaQueryWrapper = Wrappers.lambdaQuery();
        hnzsxh5OrderInfoOrderattrlistLambdaQueryWrapper.eq(Hnzsxh5OrderInfoOrderattrlist::getOrderNo, orderNo);
        //根据orderNo查询hnzsxh5_order_info_orderattrlist
        List<Hnzsxh5OrderInfoOrderattrlist> hnzsxh5OrderInfoOrderattrlists = hnzsxh5OrderInfoOrderattrlistService.list(hnzsxh5OrderInfoOrderattrlistLambdaQueryWrapper);
        //设置hnzsxh5_order_info_offerinstlist对象
        hnzsxh5OrderInfo.setHnzsxh5OrderInfoOrderattrlists(hnzsxh5OrderInfoOrderattrlists);


    }

    /**
     * 确认收费接口
     *
     * @param reqData
     * @return 确认收费接口
     */
    @PostMapping("/h5CommitFee")
    public ApiResult h5CommitFee(@RequestBody JSONObject reqData) {
        String result = hnzsxh5OrderInfoService.h5CommitFee(reqData);
        JSONObject json = JSONObject.parseObject(result);
        if (json != null && json.getInteger("code") != null && json.getInteger("code") == 0) {
            return success("手动过费接口调用成功！");
        } else {
            // 失败处理
            String msg = json != null ? json.getString("message") : "接口返回异常";
            return fail("手动过费接口调用失败: " + msg);
        }
    }
    
    /**
     * 导出订单数据为Excel
     *
     * @param param 查询参数
     * @param response HTTP响应对象
     */
    @OperationLog
    @ApiOperation("导出订单数据为Excel")
    @PostMapping("/exportOrderData")
    public void exportOrderData(@RequestBody(required = false) Hnzsxh5OrderInfoParam param, HttpServletResponse response) {
        try {
            hnzsxh5OrderInfoService.exportOrderData(param, response);
        } catch (Exception e) {
            e.printStackTrace();
            response.setContentType("application/json;charset=utf-8");
            try {
                response.getWriter().write("{\"code\": 500, \"message\": \"导出失败: " + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }
    /**
     * 解冻用户微信订单
     *
     * @param reqData
     * @return R
     * @date 2025-05-13 08:37:08
     */
    @PostMapping("/cancelCancelExamTosafe")
    @OperationLog(value ="分页查询H5即时受理-解冻用户微信订单",logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "分页查询H5即时受理/订单管理")
    public R cancelExamTosafe(@RequestBody JSONObject reqData) {
        try {
            String besttoneOrderItemNo = reqData.getString("besttoneOrderItemNo");
            if (org.apache.commons.lang3.StringUtils.isBlank(besttoneOrderItemNo)) {
                return R.error(1, "固网担保流水号不能为空");
            }
            return InterfaceUtil.cancelExamOnline(besttoneOrderItemNo);
        } catch (Exception e) {
            logger.error("解冻用户微信订单异常", e);
            return R.error(1, "解冻用户微信订单异常：" + e.getMessage());
        }
    }
    /**
     * 查询用户微信订单
     *
     * @param reqData
     * @return R
     * @date 2025-05-13 08:37:08
     */
    @PostMapping("/queryCancelExamTosafe")
    @OperationLog(value ="分页查询H5即时受理-查询用户微信订单",logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "分页查询H5即时受理/订单管理")
    public R queryCancelExamTosafe(@RequestBody JSONObject reqData) {
        try {
            // 订单信息中获取客户资料信息
            String orderNo = reqData.getString("orderNo");
            LambdaQueryWrapper<Hnzsxh5OrderInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Hnzsxh5OrderInfo::getOrderNo,orderNo);
            Hnzsxh5OrderInfo orderInfo = hnzsxh5OrderInfoService.getOne(queryWrapper);
            LambdaQueryWrapper<Hnzsxh5OrderInfoProdinstlist> prodinstWrapper = Wrappers.lambdaQuery();
            prodinstWrapper.eq(Hnzsxh5OrderInfoProdinstlist::getOrderNo, orderNo);
            //只查询主卡的产品
            prodinstWrapper.eq(Hnzsxh5OrderInfoProdinstlist::getProdId,"80000045");
            prodinstWrapper.eq(Hnzsxh5OrderInfoProdinstlist::getRoleId,"70000045");
            //根据orderNo查询hnzsxh5_order_info_prodinstlist
            List<Hnzsxh5OrderInfoProdinstlist> prolists = hnzsxh5OrderInfoProdinstlistService.list(prodinstWrapper);
            if (!CollectionUtils.isEmpty(prolists)){
                Hnzsxh5OrderInfoProdinstlist hnzsxh5OrderInfoProdinstlist = prolists.get(0);
                String prodinstId = hnzsxh5OrderInfoProdinstlist.getId();
                LambdaQueryWrapper<Hnzsxh5OrderInfoProdinstlistAttrlist> prodinstlistAttrlistWrapper = Wrappers.lambdaQuery();
                prodinstlistAttrlistWrapper.eq(Hnzsxh5OrderInfoProdinstlistAttrlist::getAttrId,"990000017");
                prodinstlistAttrlistWrapper.eq(Hnzsxh5OrderInfoProdinstlistAttrlist::getOrderInfoProdinstlistId, prodinstId);
                List<Hnzsxh5OrderInfoProdinstlistAttrlist> prodlistAttrlist = hnzsxh5OrderInfoProdinstlistAttrlistService.list(prodinstlistAttrlistWrapper);
                Hnzsxh5OrderInfoProdinstlistAttrlist prodAttr= prodlistAttrlist.get(0);
                //这里的属性值即为请求参数中要的手机号码
                reqData.put("phone",prodAttr.getAttrValue());
            }else{
                reqData.put("phone","");
            }
            reqData.put("customerCard", orderInfo.getUserCard());
            reqData.put("readType","1");
            reqData.put("type","newQuery");
            R queryCancelExamTosafe = InterfaceUtil.queryCancelExamTosafe(reqData);
            if (0 == queryCancelExamTosafe.getInteger("code")) {
                JSONArray dataArray = queryCancelExamTosafe.getJSONArray("data");
                JSONArray resultList = new JSONArray();
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject data = dataArray.getJSONObject(i);
                    if ("已授信".equals(data.getString("orderStatus"))) {
                        resultList.add(data);
                    }
                }
                return R.ok().put("data", resultList);
            } else {
                return queryCancelExamTosafe;
            }
        } catch (Exception e) {
            logger.error("查询用户微信订单异常", e);
            return R.error(1, "查询用户微信订单异常" + e.getMessage());
        }
    }

    
    /**
     * 统一变更订单状态和支付状态
     *
     * @param reqData 包含订单号、状态信息和变更原因的请求数据
     * @return ApiResult 操作结果
     * <AUTHOR>
     * @date 2025-07-01
     */
    @OperationLog(value ="H5即时受理-统一变更订单状态",logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/订单管理")
    @ApiOperation("统一变更订单状态和支付状态")
    @PostMapping("/changeOrderStatusUnified")
    public ApiResult<?> changeOrderStatusUnified(@RequestBody JSONObject reqData) {
        try {
            // 获取公共参数
            String orderNo = reqData.getString("orderNo");
            String stateChangeReason = reqData.getString("stateChangeReason");
            
            // 参数校验
            if (StringUtils.isEmpty(orderNo)) {
                return fail("订单号不能为空");
            }
            
            if (StringUtils.isEmpty(stateChangeReason)) {
                return fail("变更原因不能为空");
            }
            
            // 获取状态变更参数(非必须)
            Integer newState = reqData.getInteger("newState");
            Integer newPaymentStatus = reqData.getInteger("newPaymentStatus");
            
            // 至少需要一项变更
            if (newState == null && newPaymentStatus == null) {
                return fail("订单状态和支付状态至少需要变更一项");
            }
            
            // 调用服务统一变更状态
            boolean result = hnzsxh5OrderInfoService.changeOrderStatusUnified(
                orderNo, newState, newPaymentStatus, stateChangeReason);
            
            if (result) {
                return success("订单状态变更成功");
            } else {
                return fail("订单状态变更失败，请检查订单号是否正确");
            }
        } catch (Exception e) {
            logger.error("统一变更订单状态异常", e);
            return fail("订单状态变更失败: " + e.getMessage());
        }
    }
}