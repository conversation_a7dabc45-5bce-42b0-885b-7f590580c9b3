package com.hlkj.yxsAdminApi.hnzsxH5.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfo;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderCountParam;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderDailyParam;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderInfoParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * H5即时受理-订单基础信息Mapper
 *
 * <AUTHOR>
 * @since 2025-04-15 17:54:29
 */
public interface Hnzsxh5OrderInfoMapper extends BaseMapper<Hnzsxh5OrderInfo> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<Hnzsxh5OrderInfo>
     */
    List<Hnzsxh5OrderInfo> selectPageRel(@Param("page") IPage<Hnzsxh5OrderInfo> page,
                             @Param("param") Hnzsxh5OrderInfoParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<Hnzsxh5OrderInfo> selectListRel(@Param("param") Hnzsxh5OrderInfoParam param);



    @Select("<script>" +
            "SELECT a.CITY_CODE,GOODS_ID,GOODS_NAME,COUNT(1) saleNum " +
            "FROM hnzsxh5_order_info a " +
            "WHERE STATE = 0 " +
            "<if test='beginDate != null'>" +
            "   AND a.CREATED_DATE BETWEEN #{beginDate} AND #{endDate}" +
            "</if>" +
            "GROUP BY a.CITY_CODE,GOODS_ID,GOODS_NAME " +
            "ORDER BY CITY_CODE " +
            "</script>")
    List<Hnzsxh5OrderCountParam> getCountByGoodId(
            @Param("beginDate") String beginDate,
            @Param("endDate") String endDate
    );



    @Select("<script>" +
            "SELECT a.CITY_CODE, COUNT(1) saleNum " +
            "FROM hnzsxh5_order_info a " +
            "WHERE STATE = 0 " +
            "<if test='beginDate != null'>" +
            "   AND a.CREATED_DATE BETWEEN #{beginDate} AND #{endDate}" +
            "</if>" +
            "GROUP BY a.CITY_CODE " +
            "ORDER BY CITY_CODE " +
            "</script>")
    List<Hnzsxh5OrderCountParam> selectGoodsSalesByDate(
            @Param("beginDate") String beginDate,
            @Param("endDate") String endDate
    );

    /**
     * 获取各地市每日订单数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 各地市每日订单数量列表
     */
    @Select("<script>" +
            "SELECT DATE_FORMAT(a.CREATED_DATE, '%Y-%m-%d') as date, " +
            "a.CITY_CODE as cityCode, " +
            "COUNT(1) as orderCount " +
            "FROM hnzsxh5_order_info a " +
            "WHERE STATE = 0 " +
            "<if test='startDate != null'>" +
            "   AND DATE_FORMAT(a.CREATED_DATE, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}" +
            "</if>" +
            "GROUP BY DATE_FORMAT(a.CREATED_DATE, '%Y-%m-%d'), a.CITY_CODE " +
            "ORDER BY date, CITY_CODE " +
            "</script>")
    List<Hnzsxh5OrderDailyParam> getDailyOrdersByCityCode(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );

    /**
     * 获取各地市按模块分类的订单数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 各地市按模块分类的订单数量
     */
    @Select("<script>" +
            "SELECT a.CITY_CODE as cityCode, " +
            "COUNT(1) as totalCount, " +
            "m.MODULE_NAME as moduleName, " +
            "m.ID as moduleId, " +
            "COUNT(CASE WHEN g.MODULE_ID = m.ID THEN 1 ELSE NULL END) as moduleCount " +
            "FROM hnzsxh5_order_info a " +
            "LEFT JOIN hnzsxh5_goods_info g ON a.GOODS_ID = g.ID " +
            "LEFT JOIN hnzsxh5_module m ON g.MODULE_ID = m.ID " +
            "WHERE a.STATE = 0 " +
            "<if test='startDate != null'>" +
            "   AND a.CREATED_DATE BETWEEN CONCAT(#{startDate}, ' 00:00:00') AND CONCAT(#{endDate}, ' 23:59:59') " +
            "</if>" +
            "GROUP BY a.CITY_CODE, m.ID, m.MODULE_NAME " +
            "ORDER BY a.CITY_CODE, m.MODULE_NAME " +
            "</script>")
    List<Map<String, Object>> getOrderCountByModule(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );
    
    /**
     * 获取所有模块信息
     *
     * @return 所有模块信息列表
     */
    @Select("SELECT ID as id, MODULE_NAME as moduleName FROM hnzsxh5_module ORDER BY MODULE_NAME")
    List<Map<String, Object>> getAllModules();

    /**
     * 获取按模块名称汇总发展总量统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按模块名称汇总的订单数量统计
     */
    @Select("<script>" +
            "SELECT " +
            "COALESCE(m.MODULE_NAME, '未分类') as moduleName, " +
            "COALESCE(m.ID, 0) as moduleId, " +
            "COUNT(a.ID) as totalCount, " +
            "COUNT(DISTINCT a.CITY_CODE) as cityCount " +
            "FROM hnzsxh5_order_info a " +
            "LEFT JOIN hnzsxh5_goods_info g ON a.GOODS_ID = g.ID " +
            "LEFT JOIN hnzsxh5_module m ON g.MODULE_ID = m.ID " +
            "WHERE a.STATE = 0 " +
            "<if test='startDate != null'>" +
            "   AND a.CREATED_DATE BETWEEN CONCAT(#{startDate}, ' 00:00:00') AND CONCAT(#{endDate}, ' 23:59:59') " +
            "</if>" +
            "GROUP BY m.MODULE_NAME, m.ID " +
            "ORDER BY totalCount DESC " +
            "</script>")
    List<Map<String, Object>> getOrderCountByModuleType(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );

    /**
     * 获取按地市汇总发展总量统计 - 基础数据查询
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按地市汇总的订单数量统计基础数据
     */
    @Select("<script>" +
            "SELECT " +
            "a.CITY_CODE as cityCode, " +
            "COUNT(a.ID) as totalCount, " +
            "COUNT(DISTINCT CASE WHEN g.MODULE_ID IS NOT NULL THEN g.MODULE_ID END) as moduleCount " +
            "FROM hnzsxh5_order_info a " +
            "LEFT JOIN hnzsxh5_goods_info g ON a.GOODS_ID = g.ID " +
            "WHERE a.STATE = 0 " +
            "<if test='startDate != null'>" +
            "   AND a.CREATED_DATE BETWEEN CONCAT(#{startDate}, ' 00:00:00') AND CONCAT(#{endDate}, ' 23:59:59') " +
            "</if>" +
            "GROUP BY a.CITY_CODE " +
            "ORDER BY totalCount DESC " +
            "</script>")
    List<Map<String, Object>> getOrderCountByCity(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );
}
