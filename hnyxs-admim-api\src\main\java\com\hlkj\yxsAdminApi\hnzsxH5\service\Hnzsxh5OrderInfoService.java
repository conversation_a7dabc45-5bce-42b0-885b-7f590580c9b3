package com.hlkj.yxsAdminApi.hnzsxH5.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5OrderInfo;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderCountParam;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderDailyParam;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderInfoParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * H5即时受理-订单基础信息Service
 *
 * <AUTHOR>
 * @since 2025-04-15 17:54:29
 */
public interface Hnzsxh5OrderInfoService extends IService<Hnzsxh5OrderInfo> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<Hnzsxh5OrderInfo>
     */
    PageResult<Hnzsxh5OrderInfo> pageRel(Hnzsxh5OrderInfoParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<Hnzsxh5OrderInfo>
     */
    List<Hnzsxh5OrderInfo> listRel(Hnzsxh5OrderInfoParam param);

    /**
     * 根据id查询
     *
     * @param id 唯一标识
     * @return Hnzsxh5OrderInfo
     */
    Hnzsxh5OrderInfo getByIdRel(Integer id);

    /**
     * 根据id查询
     * @param endDate 查询结束时间
     * @param date 传入日期
     * @return Hnzsxh5OrderCountParam
     */
    List<Hnzsxh5OrderCountParam> getGoodsSalesByDate(String date,String endDate);


    /**
     * 根据id查询
     *
     * @param date 传入日期
     * @param endDate 查询结束时间
     * @return Hnzsxh5OrderCountParam
     */
    List<Hnzsxh5OrderCountParam> getCountByGoodId(String date,String endDate);

    /**
     * 获取各地市每日订单数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 各地市每日订单数量列表
     */
    List<Hnzsxh5OrderDailyParam> getDailyOrdersByCityCode(String startDate, String endDate);

    /**
     * 手动过费
     *
     * @param reqData 请求数据
     * @return 结果
     */
	String h5CommitFee(JSONObject reqData);
    
    /**
     * 导出订单数据
     *
     * @param param 查询参数
     * @param response HTTP响应对象
     */
    void exportOrderData(Hnzsxh5OrderInfoParam param, HttpServletResponse response) throws Exception;

    /**
     * 获取各地市按模块分类的订单数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 各地市按模块分类的订单数量和模块列表
     */
    Map<String, Object> getOrderCountByModule(String startDate, String endDate);

    /**
     * 获取按模块类型汇总发展总量统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按模块类型汇总的订单数量统计
     */
    List<Map<String, Object>> getOrderCountByModuleType(String startDate, String endDate);

    /**
     * 获取按地市汇总发展总量统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按地市汇总的订单数量统计
     */
    List<Map<String, Object>> getOrderCountByCity(String startDate, String endDate);

    
    /**
     * 统一变更订单状态和支付状态
     * 
     * @param orderNo 订单号
     * @param newState 新订单状态，可为null表示不变更
     * @param newPaymentStatus 新支付状态，可为null表示不变更
     * @param stateChangeReason 变更原因
     * @return 操作结果
     * <AUTHOR>
     * @date 2025-07-01
     */
    boolean changeOrderStatusUnified(String orderNo, Integer newState, Integer newPaymentStatus, String stateChangeReason);
}
