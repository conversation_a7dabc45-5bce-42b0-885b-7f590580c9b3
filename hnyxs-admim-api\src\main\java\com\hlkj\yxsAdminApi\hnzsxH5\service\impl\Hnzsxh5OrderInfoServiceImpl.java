package com.hlkj.yxsAdminApi.hnzsxH5.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.utils.ConnectionUrlUtil;
import com.hlkj.yxsAdminApi.common.core.utils.DesensitizationUtil;
import com.hlkj.yxsAdminApi.common.core.utils.StringUtil;
import com.hlkj.yxsAdminApi.common.system.entity.Dictionary;
import com.hlkj.yxsAdminApi.common.system.service.DictionaryService;
import com.hlkj.yxsAdminApi.hnzsx.service.HnzsxUserService;
import com.hlkj.yxsAdminApi.hnzsxH5.common.enums.OrderStatus;
import com.hlkj.yxsAdminApi.hnzsxH5.common.enums.PayChannelType;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.*;
import com.hlkj.yxsAdminApi.hnzsxH5.mapper.Hnzsxh5OrderInfoMapper;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderCountParam;
import com.hlkj.yxsAdminApi.hnzsxH5.service.*;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderInfoParam;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5OrderDailyParam;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * H5即时受理-订单基础信息Service实现
 *
 * <AUTHOR>
 * @since 2025-04-15 17:54:29
 */
@Service
public class Hnzsxh5OrderInfoServiceImpl extends ServiceImpl<Hnzsxh5OrderInfoMapper, Hnzsxh5OrderInfo> implements Hnzsxh5OrderInfoService {

    @Autowired
    private Hnzsxh5GoodsInfoService hnzsxh5GoodsInfoService;
    
    @Autowired
    private Hnzsxh5PaymentInfoService hnzsxh5PaymentInfoService;

    @Autowired
    private DictionaryService dictionaryService;
    
    @Autowired
    private Hnzsxh5TemplateInfoService hnzsxh5TemplateInfoService;
    
    @Autowired
    private HnzsxSysUrlConfigService hnzsxSysUrlConfigService;

    @Autowired
    private Hnzsxh5UserService hnzsxh5UserService;

    @Override
    public PageResult<Hnzsxh5OrderInfo> pageRel(Hnzsxh5OrderInfoParam param) {
        PageParam<Hnzsxh5OrderInfo, Hnzsxh5OrderInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<Hnzsxh5OrderInfo> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<Hnzsxh5OrderInfo> listRel(Hnzsxh5OrderInfoParam param) {
        List<Hnzsxh5OrderInfo> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<Hnzsxh5OrderInfo, Hnzsxh5OrderInfoParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public Hnzsxh5OrderInfo getByIdRel(Integer id) {
        Hnzsxh5OrderInfoParam param = new Hnzsxh5OrderInfoParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Override
    public List<Hnzsxh5OrderCountParam> getGoodsSalesByDate(String date,String endDate) {
        String beginDate = null;
        if (!StringUtil.isEmpty(date)) {

            //拼接开始时间
            beginDate = date +" 00:00:00";
            if(StringUtils.isEmpty(endDate)){
                endDate = date +" 23:59:59";
            }else{
                endDate = endDate +" 23:59:59";
            }

            return baseMapper.selectGoodsSalesByDate(beginDate,endDate);
        }else{
            return baseMapper.selectGoodsSalesByDate(beginDate,endDate);
        }
    }

    @Override
    public List<Hnzsxh5OrderCountParam> getCountByGoodId(String date,String endDate) {
        String beginDate = null;
        if (!StringUtil.isEmpty(date)) {

            //拼接开始时间
            beginDate = date +" 00:00:00";
            if(StringUtils.isEmpty(endDate)){
                endDate = date +" 23:59:59";
            }else{
                endDate = endDate +" 23:59:59";
            }
            return baseMapper.getCountByGoodId(beginDate,endDate);
        }else{
            return baseMapper.getCountByGoodId(beginDate,endDate);
        }
    }

    @Override
    public String h5CommitFee(JSONObject reqData) {
        //调用h5即使受理服务端确认收费接口
        String orderNo = reqData.getString("orderNo");
        
        if (StringUtils.isEmpty(orderNo)) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "订单号不能为空");
            return errorResult.toJSONString();
        }
        
        // 1. 根据订单号查询订单信息
        LambdaQueryWrapper<Hnzsxh5OrderInfo> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(Hnzsxh5OrderInfo::getOrderNo, orderNo);
        Hnzsxh5OrderInfo orderInfo = this.getOne(orderWrapper);
        
        if (orderInfo == null) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "未找到订单信息，orderNo: " + orderNo);
            return errorResult.toJSONString();
        }

        // 校验订单状态是否为-8（订单收费确认失败）
        if (orderInfo.getState() != -8) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "当前订单状态不允许手动过费，orderNo: " + orderNo);
            return errorResult.toJSONString();
        }

        // 2. 根据订单号查询支付流水信息
        Hnzsxh5PaymentInfo paymentInfo = hnzsxh5PaymentInfoService.getByOrderNo(orderNo);
        
        if (paymentInfo == null) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "未找到支付流水信息，orderNo: " + orderNo);
            return errorResult.toJSONString();
        }

        // 3. 更新手动过费相关信息
        orderInfo.setIsManualFee(1); // 标记为已手动过费（1:是）
        orderInfo.setManualFeeCount(orderInfo.getManualFeeCount() == null ? 1 : orderInfo.getManualFeeCount() + 1);
        this.updateById(orderInfo);

        // 4. 构造请求参数
        try {
            // 金额转换：元 -> 分（1元=100分）
            String totalAmountYuan = orderInfo.getTotalAmount();
            if (StringUtils.isEmpty(totalAmountYuan)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "订单金额不能为空");
                return errorResult.toJSONString();
            }
            
            // 转换金额：元 -> 分
            BigDecimal amountYuan = new BigDecimal(totalAmountYuan);
            BigDecimal amountFen = amountYuan.multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP);
            String cashFee = amountFen.toString();
            
            // 获取支付渠道类型
            String payChannelType = PayChannelType.getPayChannelType(orderInfo.getPaymentMethod());
            String paymentTransactionId = orderInfo.getPaymentTransactionId();
            String payUserSerial = paymentInfo.getPayUserSerial();
            String transSeq = paymentInfo.getTransSeq();
            String upTransSeq = paymentInfo.getUpTransSeq();
            
            // 参数校验
            if (StringUtils.isEmpty(payChannelType)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "支付渠道类型不能为空");
                return errorResult.toJSONString();
            }
            if (StringUtils.isEmpty(paymentTransactionId)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "支付流水号不能为空");
                return errorResult.toJSONString();
            }
            if (StringUtils.isEmpty(payUserSerial)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "支付用户序列号不能为空");
                return errorResult.toJSONString();
            }
            if (StringUtils.isEmpty(transSeq)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "交易序列号不能为空");
                return errorResult.toJSONString();
            }
            if (StringUtils.isEmpty(upTransSeq)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "上游交易序列号不能为空");
                return errorResult.toJSONString();
            }
            
            // 设置请求参数
            reqData.put("cashFee", cashFee);
            reqData.put("paySerialId", paymentTransactionId);
            reqData.put("payChannelType", payChannelType);
            reqData.put("resultCode", 0);
            reqData.put("preTransactionId", payUserSerial);
            reqData.put("transSeq", transSeq);
            reqData.put("upTransSeq", upTransSeq);
            
            // 5. 获取手动过费接口URL
            LambdaQueryWrapper<HnzsxSysUrlConfigEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HnzsxSysUrlConfigEntity::getUrlCode, "MANUAL_FEE_URL");
            HnzsxSysUrlConfigEntity urlConfigEntity = hnzsxSysUrlConfigService.getOne(wrapper);

            if (urlConfigEntity == null || StringUtils.isEmpty(urlConfigEntity.getFullUrl())) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", Constants.RESULT_ERROR_CODE);
                errorResult.put("message", "未找到手动过费接口URL配置");
                return errorResult.toJSONString();
            }
            
            // 6. 发送请求
            return ConnectionUrlUtil.sendJsonPost(urlConfigEntity.getFullUrl(), reqData.toJSONString());
            
        } catch (NumberFormatException e) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "订单金额格式错误，无法转换为数字: " + orderInfo.getTotalAmount());
            return errorResult.toJSONString();
        } catch (Exception e) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", Constants.RESULT_ERROR_CODE);
            errorResult.put("message", "手动过费处理异常: " + e.getMessage());
            return errorResult.toJSONString();
        }
    }


    
    @Override
    public void exportOrderData(Hnzsxh5OrderInfoParam param, HttpServletResponse response) {
        try {
            // 查询数据
            List<Hnzsxh5OrderInfo> orderList;
            
            if (param != null) {
                PageParam<Hnzsxh5OrderInfo, Hnzsxh5OrderInfoParam> page = new PageParam<>(param);
                page.setDefaultOrder("createdDate desc");
                orderList = this.list(page.getOrderWrapper());
            } else {
                // 如果没有参数，则查询所有数据
                orderList = this.list();
            }
            
            // 对敏感字段进行脱敏处理
            for (Hnzsxh5OrderInfo orderInfo : orderList) {
                // 调用脱敏方法
                desensitizeOrderInfo(orderInfo);
            }
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建工作表
            Sheet sheet = workbook.createSheet("订单数据");
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            
            // 创建表头
            String[] headers = {"时间", "地市", "订单号", "87单号", "商品分类名称", "商品模块名称", "商品名称", "模板ID", "模板名称", "礼包ID", "店中商编码", "店中商名称", "工号", "工号发展人姓名", "用户姓名", "用户手机号", "用户身份证", "订单状态", "失败原因", "订单金额"};
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                // 设置列宽
                sheet.setColumnWidth(i, 256 * 20);
            }
            
            // 日期格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            // 填充数据
            int rowNum = 1;
            // 缓存减少数据库查询
            Map<String, Map<String, String>> templateInfoCache = new HashMap<>();
            Map<Integer, String> userCompanyNameCache = new HashMap<>();
            Map<Integer, Hnzsxh5GoodsInfo> goodsInfoCache = new HashMap<>();
            
            for (Hnzsxh5OrderInfo order : orderList) {
                Row row = sheet.createRow(rowNum++);
                
                // 1. 时间（订单表created_date字段）
                Cell cell0 = row.createCell(0);
                if (order.getCreatedDate() != null) {
                    cell0.setCellValue(order.getCreatedDate().format(formatter));
                }
                
                // 2. 地市(订单表city_name字段)
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(order.getCityName());
                
                // 3. 订单号
                Cell cell2 = row.createCell(2);
                cell2.setCellValue(order.getOrderNo() != null ? order.getOrderNo() : "");
                
                // 4. 87单号（订单表CUST_ORDER_ID_LIST字段）
                Cell cell3 = row.createCell(3);
                cell3.setCellValue(order.getCustOrderIdList() != null ? order.getCustOrderIdList() : "");
                
                // 获取商品信息 - 商品分类和礼包ID
                String moduleTypeName = "";
                String giftPackageIds = "";
                if (order.getGoodsId() != null) {
                    // 从缓存获取或查询商品信息
                    Hnzsxh5GoodsInfo goodsInfo;
                    if (goodsInfoCache.containsKey(order.getGoodsId())) {
                        goodsInfo = goodsInfoCache.get(order.getGoodsId());
                    } else {
                        goodsInfo = hnzsxh5GoodsInfoService.getById(order.getGoodsId());
                        if (goodsInfo != null) {
                            goodsInfoCache.put(order.getGoodsId(), goodsInfo);
                        }
                    }
                    
                    if (goodsInfo != null) {
                        moduleTypeName = goodsInfo.getModuleTypeName();
                        giftPackageIds = goodsInfo.getGiftPackageIds();
                    }
                }
                
                // 5. 商品分类名称（商品表MODULE_TYPE_NAME字段）
                Cell cell4 = row.createCell(4);
                cell4.setCellValue(moduleTypeName);
                
                // 6. 商品模块名称（业务类型：模块类型moudle_name字段）
                Cell cell5 = row.createCell(5);
                cell5.setCellValue(order.getModuleName());
                
                // 7. 商品名称（goods_name）
                Cell cell6 = row.createCell(6);
                cell6.setCellValue(order.getGoodsName());
                
                // 模板相关信息
                String templateIds = "";
                String templateNames = "";
                
                if (StringUtils.isNotBlank(order.getTemplateInfoIdList())) {
                    // 从缓存中获取或查询模板信息
                    if (templateInfoCache.containsKey(order.getTemplateInfoIdList())) {
                        Map<String, String> infoMap = templateInfoCache.get(order.getTemplateInfoIdList());
                        templateIds = infoMap.get("templateIds");
                        templateNames = infoMap.get("templateNames");
                    } else {
                        List<String> templateIdList = new ArrayList<>();
                        List<String> templateNameList = new ArrayList<>();
                        
                        String[] templateInfoIds = order.getTemplateInfoIdList().split(",");
                        for (String templateInfoId : templateInfoIds) {
                            if (StringUtils.isNotBlank(templateInfoId)) {
                                try {
                                    Hnzsxh5TemplateInfo templateInfo = hnzsxh5TemplateInfoService.getById(Integer.parseInt(templateInfoId));
                                    if (templateInfo != null) {
                                        // 添加模板ID
                                        if (StringUtils.isNotBlank(templateInfo.getTemplateId())) {
                                            templateIdList.add(templateInfo.getTemplateId());
                                        }
                                        
                                        // 添加模板名称
                                        if (StringUtils.isNotBlank(templateInfo.getTemplateName())) {
                                            templateNameList.add(templateInfo.getTemplateName());
                                        }
                                    }
                                } catch (Exception e) {
                                    // 处理可能的异常
                                    continue;
                                }
                            }
                        }
                        templateIds = String.join(",", templateIdList);
                        templateNames = String.join(",", templateNameList);
                        
                        // 保存到缓存
                        Map<String, String> infoMap = new HashMap<>();
                        infoMap.put("templateIds", templateIds);
                        infoMap.put("templateNames", templateNames);
                        templateInfoCache.put(order.getTemplateInfoIdList(), infoMap);
                    }
                }
                
                // 8. 模板ID
                Cell cell7 = row.createCell(7);
                cell7.setCellValue(templateIds);
                
                // 9. 模板名称
                Cell cell8 = row.createCell(8);
                cell8.setCellValue(templateNames);
                
                // 10. 礼包ID（商品表GIFT_PACKAGE_IDS字段）
                Cell cell9 = row.createCell(9);
                cell9.setCellValue(giftPackageIds);
                
                // 11. 店中商编码（订单表的LOGIN_SALE_BOX_CODE字段）
                Cell cell10 = row.createCell(10);
                cell10.setCellValue(order.getLoginSaleBoxCode());
                
                // 12. 店中商名称(订单表的login_user_id字段关联hnzsx_h5_user表拿到其COMPANY_NAME)
                Cell cell11 = row.createCell(11);
                String companyName = "";
                if (order.getLoginUserId() != null) {
                    // 从缓存中获取或查询用户信息
                    if (userCompanyNameCache.containsKey(order.getLoginUserId())) {
                        companyName = userCompanyNameCache.get(order.getLoginUserId());
                    } else {
                        Hnzsxh5User user = hnzsxh5UserService.getById(order.getLoginUserId());
                        if (user != null) {
                            companyName = user.getCompanyName();
                            userCompanyNameCache.put(order.getLoginUserId(), companyName);
                        }
                    }
                }
                cell11.setCellValue(companyName);
                
                // 13. 工号（订单表LOGIN_STAFF_CODE）
                Cell cell12 = row.createCell(12);
                cell12.setCellValue(order.getLoginStaffCode());
                
                // 14. 工号发展人姓名（订单表LOGIN_USER_NAME字段）
                Cell cell13 = row.createCell(13);
                cell13.setCellValue(order.getLoginUserName());
                
                // 15. 用户姓名
                Cell cell14 = row.createCell(14);
                cell14.setCellValue(order.getUserName() != null ? order.getUserName() : "");
                
                // 16. 用户手机号
                Cell cell15 = row.createCell(15);
                cell15.setCellValue(order.getUserMoble() != null ? order.getUserMoble() : "");
                
                // 17. 用户身份证
                Cell cell16 = row.createCell(16);
                cell16.setCellValue(order.getUserCard() != null ? order.getUserCard() : "");
                
                // 18. 订单状态
                Cell cell17 = row.createCell(17);
                if (order.getState() != null) {
                    cell17.setCellValue(OrderStatus.getDescriptionByCode(order.getState()));
                }
                
                
                // 19. 失败原因
                Cell cell18 = row.createCell(18);
                cell18.setCellValue(order.getFailReason() != null ? order.getFailReason() : "");
                
                // 20. 订单金额
                Cell cell19 = row.createCell(19);
                cell19.setCellValue(order.getTotalAmount() != null ? order.getTotalAmount() : "");
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("订单数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            
            // 写入响应输出流
            workbook.write(response.getOutputStream());
            workbook.close();
            
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("导出Excel失败");
        }
    }
    
    /**
     * 对订单信息中的敏感数据进行脱敏处理
     *
     * @param orderInfo 订单信息对象
     */
    private void desensitizeOrderInfo(Hnzsxh5OrderInfo orderInfo) {
        if (orderInfo == null) {
            return;
        }
        
        // 对订单基本信息中的敏感字段进行脱敏
        orderInfo.setUserName(DesensitizationUtil.desensitizedName(orderInfo.getUserName()));
        orderInfo.setUserMoble(DesensitizationUtil.mobilePhoneDesensitization(orderInfo.getUserMoble()));
        orderInfo.setUserCard(DesensitizationUtil.idCardDesensitization(orderInfo.getUserCard()));
        orderInfo.setUserAddress(DesensitizationUtil.addressDesensitization(orderInfo.getUserAddress()));
        orderInfo.setLoginUserName(DesensitizationUtil.desensitizedName(orderInfo.getLoginUserName()));
        orderInfo.setLoginUserPhone(DesensitizationUtil.mobilePhoneDesensitization(orderInfo.getLoginUserPhone()));
        
        if (StringUtils.isNotEmpty(orderInfo.getReceptInvoiceEmail())) {
            orderInfo.setReceptInvoiceEmail(DesensitizationUtil.emailDesensitization(orderInfo.getReceptInvoiceEmail()));
        }
    }

    @Override
    public List<Hnzsxh5OrderDailyParam> getDailyOrdersByCityCode(String startDate, String endDate) {
        List<Hnzsxh5OrderDailyParam> result = baseMapper.getDailyOrdersByCityCode(startDate, endDate);
        
        // 为每个订单项添加城市名称
        for (Hnzsxh5OrderDailyParam param : result) {
            if (param.getCityCode() != null) {
                switch (param.getCityCode()) {
                    case "730":
                        param.setCityName("岳阳");
                        break;
                    case "731":
                        param.setCityName("长沙");
                        break;
                    case "732":
                        param.setCityName("湘潭");
                        break;
                    case "733":
                        param.setCityName("株洲");
                        break;
                    case "734":
                        param.setCityName("衡阳");
                        break;
                    case "735":
                        param.setCityName("郴州");
                        break;
                    case "736":
                        param.setCityName("常德");
                        break;
                    case "737":
                        param.setCityName("益阳");
                        break;
                    case "738":
                        param.setCityName("娄底");
                        break;
                    case "739":
                        param.setCityName("邵阳");
                        break;
                    case "743":
                        param.setCityName("湘西");
                        break;
                    case "744":
                        param.setCityName("张家界");
                        break;
                    case "745":
                        param.setCityName("怀化");
                        break;
                    case "746":
                        param.setCityName("永州");
                        break;
                    default:
                        param.setCityName(param.getCityCode());
                }
            }
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getOrderCountByModule(String startDate, String endDate) {
        // 获取所有模块信息
        List<Map<String, Object>> modules = baseMapper.getAllModules();
        
        // 获取订单统计数据
        List<Map<String, Object>> orderCounts = baseMapper.getOrderCountByModule(startDate, endDate);
        
        // 按地市代码分组
        Map<String, Map<String, Object>> cityDataMap = new HashMap<>();
        
        // 填充每个城市的数据
        for (Map<String, Object> orderCount : orderCounts) {
            String cityCode = (String) orderCount.get("cityCode");
            String moduleId = String.valueOf(orderCount.get("moduleId"));
            Integer moduleCount = ((Number) orderCount.get("moduleCount")).intValue();
            
            // 如果地市不存在，创建新的地市数据
            Map<String, Object> cityData = cityDataMap.computeIfAbsent(cityCode, k -> {
                Map<String, Object> data = new HashMap<>();
                data.put("cityCode", cityCode);
                data.put("cityName", getCityName(cityCode));
                data.put("totalCount", 0);
                data.put("modules", new HashMap<String, Integer>());
                return data;
            });
            
            // 更新地市的总订单数
            Integer totalCount = ((Number) cityData.get("totalCount")).intValue();
            cityData.put("totalCount", totalCount + moduleCount);
            
            // 更新地市的模块数据
            @SuppressWarnings("unchecked")
            Map<String, Integer> cityModules = (Map<String, Integer>) cityData.get("modules");
            cityModules.put(moduleId, moduleCount);
        }
        
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("modules", modules);
        result.put("cityData", new ArrayList<>(cityDataMap.values()));
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getOrderCountByModuleType(String startDate, String endDate) {
        // 调用Mapper方法获取按模块类型汇总的订单统计
        List<Map<String, Object>> moduleTypeStats = baseMapper.getOrderCountByModuleType(startDate, endDate);

        // 如果没有数据，返回空列表
        if (moduleTypeStats == null || moduleTypeStats.isEmpty()) {
            return new ArrayList<>();
        }

        // 计算总订单数和总地市数
        int totalOrders = 0;
        int totalCities = 0;
        for (Map<String, Object> stat : moduleTypeStats) {
            totalOrders += ((Number) stat.get("totalCount")).intValue();
            totalCities += ((Number) stat.get("cityCount")).intValue();
        }

        // 为每个模块类型添加占比信息
        for (Map<String, Object> stat : moduleTypeStats) {
            int count = ((Number) stat.get("totalCount")).intValue();
            double percentage = totalOrders > 0 ? (double) count / totalOrders * 100 : 0;
            stat.put("percentage", Math.round(percentage * 100.0) / 100.0); // 保留两位小数
        }

        return moduleTypeStats;
    }

    @Override
    public List<Map<String, Object>> getOrderCountByCity(String startDate, String endDate) {
        // 使用与getOrderCountByModule相同的逻辑来确保数据一致性
        // 获取订单统计数据（按地市和模块分组）
        List<Map<String, Object>> orderCounts = baseMapper.getOrderCountByModule(startDate, endDate);

        // 如果没有数据，返回空列表
        if (orderCounts == null || orderCounts.isEmpty()) {
            return new ArrayList<>();
        }

        // 按地市代码分组并汇总
        Map<String, Map<String, Object>> cityDataMap = new HashMap<>();

        // 填充每个城市的数据
        for (Map<String, Object> orderCount : orderCounts) {
            String cityCode = (String) orderCount.get("cityCode");
            Integer moduleCount = ((Number) orderCount.get("moduleCount")).intValue();

            // 如果地市不存在，创建新的地市数据
            Map<String, Object> cityData = cityDataMap.computeIfAbsent(cityCode, k -> {
                Map<String, Object> data = new HashMap<>();
                data.put("cityCode", cityCode);
                data.put("cityName", getCityName(cityCode));
                data.put("totalCount", 0);
                data.put("moduleCount", 0);
                return data;
            });

            // 更新地市的总订单数
            Integer totalCount = ((Number) cityData.get("totalCount")).intValue();
            cityData.put("totalCount", totalCount + moduleCount);

            // 更新地市的模块数量（只计算有订单的模块）
            if (moduleCount > 0) {
                Integer currentModuleCount = ((Number) cityData.get("moduleCount")).intValue();
                cityData.put("moduleCount", currentModuleCount + 1);
            }
        }

        // 转换为列表并计算占比
        List<Map<String, Object>> cityStats = new ArrayList<>(cityDataMap.values());

        // 计算总订单数
        int totalOrders = 0;
        for (Map<String, Object> stat : cityStats) {
            totalOrders += ((Number) stat.get("totalCount")).intValue();
        }

        // 为每个地市添加占比信息
        for (Map<String, Object> stat : cityStats) {
            int count = ((Number) stat.get("totalCount")).intValue();
            double percentage = totalOrders > 0 ? (double) count / totalOrders * 100 : 0;
            stat.put("percentage", Math.round(percentage * 100.0) / 100.0); // 保留两位小数
        }

        // 按订单总量降序排列
        cityStats.sort((a, b) -> {
            Integer countA = ((Number) a.get("totalCount")).intValue();
            Integer countB = ((Number) b.get("totalCount")).intValue();
            return countB.compareTo(countA);
        });

        return cityStats;
    }

    // 获取城市名称的工具方法
    private String getCityName(String cityCode) {
        if (cityCode == null) return "未知";

        switch (cityCode) {
            case "730": return "岳阳";
            case "731": return "长沙";
            case "732": return "湘潭";
            case "733": return "株洲";
            case "734": return "衡阳";
            case "735": return "郴州";
            case "736": return "常德";
            case "737": return "益阳";
            case "738": return "娄底";
            case "739": return "邵阳";
            case "743": return "湘西";
            case "744": return "张家界";
            case "745": return "怀化";
            case "746": return "永州";
            default: return cityCode;
        }
    }


    @Override
    public boolean changeOrderStatusUnified(String orderNo, Integer newState, Integer newPaymentStatus, String stateChangeReason) {
        if (StringUtils.isEmpty(orderNo) || (newState == null && newPaymentStatus == null) || StringUtils.isEmpty(stateChangeReason)) {
            return false;
        }
        
        try {
            // 查询订单信息
            LambdaQueryWrapper<Hnzsxh5OrderInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Hnzsxh5OrderInfo::getOrderNo, orderNo);
            Hnzsxh5OrderInfo orderInfo = this.getOne(queryWrapper);
            
            if (orderInfo == null) {
                return false;
            }
            
            // 更新订单状态
            if (newState != null) {
                orderInfo.setState(newState);
            }
            
            // 更新支付状态
            if (newPaymentStatus != null) {
                orderInfo.setPaymentStatus(newPaymentStatus);
                
                // 根据支付状态更新支付状态文本
                switch (newPaymentStatus) {
                    case 1:
                        orderInfo.setPaymentStatusName("支付成功");
                        break;
                    case 2:
                        orderInfo.setPaymentStatusName("支付失败");
                        break;
                    case 3:
                        orderInfo.setPaymentStatusName("费用0，无需支付");
                        break;
                    default:
                        orderInfo.setPaymentStatusName("未支付");
                }
                
                // 如果支付成功，设置支付时间为当前时间
                if (newPaymentStatus == 1 && orderInfo.getPaymentTime() == null) {
                    orderInfo.setPaymentTime(LocalDateTime.now());
                }
            }
            
            // 更新状态变更原因和时间
            StringBuilder reasonBuilder = new StringBuilder();
            
            // 获取现有原因
            String existingReason = orderInfo.getStateChangeReason();
            if (StringUtils.isNotEmpty(existingReason)) {
                reasonBuilder.append(existingReason).append("; ");
            }
            
            // 添加新的变更原因
            if (newState != null && newPaymentStatus != null) {
                // 如果同时变更了两种状态
                reasonBuilder.append("订单状态和支付状态变更: ").append(stateChangeReason);
            } else if (newState != null) {
                // 如果只变更了订单状态
                reasonBuilder.append("订单状态变更: ").append(stateChangeReason);
            } else {
                // 如果只变更了支付状态
                reasonBuilder.append("支付状态变更: ").append(stateChangeReason);
            }
            
            orderInfo.setStateChangeReason(reasonBuilder.toString());
            orderInfo.setLastStateChangeTime(LocalDateTime.now());
            
            // 更新订单信息
            return this.updateById(orderInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
