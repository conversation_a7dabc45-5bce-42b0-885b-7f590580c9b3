import request from '@/utils/request';
import { hasPermission } from '@/utils/permission';
import { API_BASE_URL } from '@/config/setting';
import { getToken } from '@/utils/token-util';

// 通用下载URL
export const getDaoUsers = `${API_BASE_URL}/download/exportDaoUsers`;

// 权限前缀
const permPrefix = 'hnzsxH5:hnzsxh5OrderInfo:';

/**
 * 分页查询订单信息
 * @param data 查询条件
 */
export async function pageOrders(data) {
  // 检查权限
  if (!hasPermission(permPrefix + 'list')) {
    return {
      list: [],
      count: 0
    };
  }

  // 调试日志
  console.log('pageOrders请求参数:', JSON.stringify(data));

  // 确保state和paymentStatus为数字类型
  if (data.state !== undefined && data.state !== null && data.state !== '') {
    data.state = Number(data.state);
  }

  if (data.paymentStatus !== undefined && data.paymentStatus !== null && data.paymentStatus !== '') {
    data.paymentStatus = Number(data.paymentStatus);
  }

  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/page', data, {
    headers: { 'Content-Type': 'application/json' }
  });

  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取所有订单列表
 * @param data 查询条件
 */
export async function listAllOrders(data) {
  // 检查权限
  if (!hasPermission(permPrefix + 'list')) {
    return [];
  }

  const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getAllListByParam', data, {
    headers: { 'Content-Type': 'application/json' }
  });

  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID获取订单详情
 * @param {Object} data 包含id的对象
 * @returns {Promise<Object>} 订单详情
 */
export async function getOrderDetail(data) {
  // 添加错误处理
  if (!data || data.id === undefined || data.id === null) {
    console.error('getOrderDetail调用错误: 无效的参数', data);
    return Promise.reject(new Error('参数错误：订单ID不能为空'));
  }

  // 检查权限
  if (!hasPermission(permPrefix + 'list')) {
    return {};
  }

  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getInfoById', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取订单详情失败'));
  } catch (error) {
    console.error('getOrderDetail请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取订单统计数据
 * @param {Object} data 包含日期的对象
 * @returns {Promise<Array>} 订单统计数据
 */
export async function getOrderCount(data) {
  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getCount', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取订单统计数据失败'));
  } catch (error) {
    console.error('getOrderCount请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取各地市每日订单数量
 * @param {Object} data 包含日期的对象
 * @returns {Promise<Array>} 各地市每日订单数据
 */
export async function getDailyOrdersByCityCode(data) {
  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getDailyOrdersByCityCode', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取各地市每日订单数据失败'));
  } catch (error) {
    console.error('getDailyOrdersByCityCode请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取各地市按模块分类的订单数量
 * @param {Object} data 包含日期的对象
 * @returns {Promise<Object>} 各地市按模块分类的订单数据
 */
export async function getOrderCountByModule(data) {
  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModule', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取各地市按模块分类订单数据失败'));
  } catch (error) {
    console.error('getOrderCountByModule请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取按模块类型汇总发展总量统计
 * @param {Object} data 包含日期的对象
 * @returns {Promise<Array>} 按模块类型汇总的订单统计数据
 */
export async function getOrderCountByModuleType(data) {
  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取按模块类型汇总统计数据失败'));
  } catch (error) {
    console.error('getOrderCountByModuleType请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取按地市汇总发展总量统计
 * @param {Object} data 包含日期的对象
 * @returns {Promise<Array>} 按地市汇总的订单统计数据
 */
export async function getOrderCountByCity(data) {
  try {
    const res = await request.post('/hnzsxH5/hnzsxh5-order-info/getOrderCountByCity', data, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message || '获取按地市汇总统计数据失败'));
  } catch (error) {
    console.error('getOrderCountByCity请求异常:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取订单状态映射
 */
export function getOrderStatusMap() {
  return {
    0: { text: '完结', type: 'success' },
    1: { text: '暂存单', type: 'info' },
    2: { text: '订单确认', type: 'primary' },
    3: { text: '未支付', type: 'warning' },
    4: { text: '已支付', type: 'success' },
    [-2]: { text: '已删除', type: 'danger' },
    [-3]: { text: '生成暂存失败', type: 'danger' },
    [-4]: { text: '订单校验失败', type: 'danger' },
    [-5]: { text: '订单生单失败', type: 'danger' },
    [-6]: { text: '订单费用计算失败', type: 'danger' },
    [-7]: { text: '订单免填单生成失败', type: 'danger' },
    [-8]: { text: '订单收费确认失败', type: 'danger' }
  };
}

/**
 * 获取支付状态映射
 */
export function getPaymentStatusMap() {
  return {
    2: { text: '未支付', type: 'warning' },
    1: { text: '已支付', type: 'success' },
    3: { text: '费用0，无需支付', type: 'info' },
    null: { text: '未支付', type: 'warning' },
    undefined: { text: '未支付', type: 'warning' }
  };
}

// 手动过费
export function manualPayment(data) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/h5CommitFee',
    method: 'post',
    data: data
  }).then(res => {
    return res.data;
  }).catch(error => {
    console.error('手动过费请求异常:', error);
    return Promise.reject(error);
  });
}

/**
 * 导出订单数据为Excel
 * @param {Object} data 查询条件
 */
export function exportOrderData(data) {
  // 创建导出URL
  let url = `/hnzsxH5/hnzsxh5-order-info/exportOrderData`;

  // 如果有查询参数，转换成JSON字符串
  let config = {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken()
    }
  };

  return request.post(url, data || {}, config);
}

/**
 * 查询暂存单信息
 * @param {string} orderNo 订单号
 * @returns {Promise<Object>} 暂存单信息
 * <AUTHOR>
 * @date 2025-05-19
 */
export function queryCancelExamTosafe(orderNo) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/queryCancelExamTosafe',
    method: 'post',
    data: { orderNo }
  });
}

/**
 * 解冻用户微信订单
 * @param {string} besttoneOrderItemNo 固网担保流水号
 * @returns {Promise<Object>} 解冻结果
 * <AUTHOR>
 * @date 2025-05-21
 */
export function cancelExamTosafe(besttoneOrderItemNo) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/cancelCancelExamTosafe',
    method: 'post',
    data: { besttoneOrderItemNo }
  });
}

/**
 * 变更订单状态
 * @param {Object} data 包含订单号、新状态和变更原因的对象
 * @returns {Promise<Object>} 变更结果
 * <AUTHOR>
 * @date 2025-06-01
 */
export function changeOrderState(data) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderState',
    method: 'post',
    data: data
  }).then(res => {
    return res.data;
  }).catch(error => {
    console.error('订单状态变更请求异常:', error);
    return Promise.reject(error);
  });
}

/**
 * 变更订单支付状态
 * @param {Object} data 包含订单号、新支付状态和变更原因的对象
 * @returns {Promise<Object>} 变更结果
 * <AUTHOR>
 * @date 2025-06-28
 */
export function changeOrderPaymentStatus(data) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderPaymentStatus',
    method: 'post',
    data: data
  }).then(res => {
    return res.data;
  }).catch(error => {
    console.error('订单支付状态变更请求异常:', error);
    return Promise.reject(error);
  });
}

/**
 * 统一变更订单状态和支付状态
 * @param {Object} data 包含订单号、新状态、新支付状态和变更原因的对象
 * @returns {Promise<Object>} 变更结果
 * <AUTHOR>
 * @date 2025-07-01
 */
export function changeOrderStatusUnified(data) {
  return request({
    url: '/hnzsxH5/hnzsxh5-order-info/changeOrderStatusUnified',
    method: 'post',
    data: data
  }).then(res => {
    return res.data;
  }).catch(error => {
    console.error('统一变更订单状态请求异常:', error);
    return Promise.reject(error);
  });
}
