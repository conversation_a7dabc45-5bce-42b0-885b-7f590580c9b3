# 订单统计模块类型汇总功能说明

## 功能概述

在原有的订单统计模块基础上，新增了"按模块类型汇总发展总量统计"功能，用于统计不同模块类型的订单发展情况。

## 新增功能

### 1. 后端接口

#### 新增Controller接口
- **接口路径**: `/api/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType`
- **请求方式**: POST
- **功能**: 获取按模块类型汇总发展总量统计
- **参数**: 
  ```json
  {
    "date": "2025-01-01",
    "endDate": "2025-01-31"
  }
  ```
- **返回数据**:
  ```json
  [
    {
      "moduleTypeName": "智能终端",
      "moduleTypeCode": "SMART_TERMINAL", 
      "totalCount": 1500,
      "cityCount": 12,
      "percentage": 35.5
    }
  ]
  ```

#### 新增Service方法
- `getOrderCountByModuleType(String startDate, String endDate)`: 获取按模块类型汇总的订单统计

#### 新增Mapper方法
- `getOrderCountByModuleType(@Param("startDate") String startDate, @Param("endDate") String endDate)`: 数据库查询方法

### 2. 前端功能

#### 新增API接口
- `getOrderCountByModuleType(data)`: 调用后端接口获取模块类型汇总数据

#### 新增页面组件
在订单统计页面(`statistics.vue`)中新增了：
- **模块类型汇总统计表格**: 展示各模块类型的订单总量、涉及地市数、占比等信息
- **数据字段**:
  - 序号
  - 模块类型名称
  - 模块类型编码  
  - 订单总量（高亮显示）
  - 涉及地市数
  - 占比（百分比，绿色显示）

#### 新增样式
- `.total-count-highlight`: 订单总量高亮样式
- `.percentage-text`: 占比文字样式

## 数据库查询逻辑

查询逻辑基于以下表关联：
1. `hnzsxh5_order_info` (订单表) 
2. `hnzsxh5_goods_info` (商品表)
3. 通过商品表中的`MODULE_TYPE_NAME`和`MODULE_TYPE_CODE`字段进行模块类型分组统计

## 功能特点

1. **实时统计**: 支持按日期范围查询
2. **多维度展示**: 包含订单总量、地市覆盖数、占比等维度
3. **可视化友好**: 重要数据高亮显示，占比用百分比形式展示
4. **响应式设计**: 表格支持响应式布局
5. **数据完整性**: 对于未分类的模块类型显示为"未分类"

## 使用方式

1. 进入订单统计页面
2. 选择统计日期范围
3. 查看"按模块类型汇总发展总量统计"表格
4. 表格按订单总量降序排列，方便查看主要业务模块的发展情况

## 技术实现

- **后端**: Spring Boot + MyBatis-Plus
- **前端**: Vue.js + Element UI
- **数据库**: MySQL
- **图表**: ECharts (预留扩展)

## 扩展建议

1. 可以考虑添加模块类型的饼图或柱状图展示
2. 支持导出模块类型统计数据
3. 添加模块类型的趋势分析功能
4. 支持按地市筛选模块类型统计
