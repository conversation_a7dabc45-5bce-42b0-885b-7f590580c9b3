# 订单统计模块类型汇总功能说明

## 功能概述

在原有的订单统计模块基础上，新增了"按模块类型汇总发展总量统计"功能，用于统计不同模块类型的订单发展情况。

## 新增功能

### 1. 后端接口

#### 新增Controller接口

**按模块类型汇总统计接口**
- **接口路径**: `/api/hnzsxH5/hnzsxh5-order-info/getOrderCountByModuleType`
- **请求方式**: POST
- **功能**: 获取按模块类型汇总发展总量统计
- **参数**:
  ```json
  {
    "date": "2025-01-01",
    "endDate": "2025-01-31"
  }
  ```
- **返回数据**:
  ```json
  [
    {
      "moduleTypeName": "智能终端",
      "moduleTypeCode": "SMART_TERMINAL",
      "totalCount": 1500,
      "cityCount": 12,
      "percentage": 35.5
    }
  ]
  ```

**按地市汇总统计接口**
- **接口路径**: `/api/hnzsxH5/hnzsxh5-order-info/getOrderCountByCity`
- **请求方式**: POST
- **功能**: 获取按地市汇总发展总量统计
- **参数**:
  ```json
  {
    "date": "2025-01-01",
    "endDate": "2025-01-31"
  }
  ```
- **返回数据**:
  ```json
  [
    {
      "cityCode": "731",
      "cityName": "长沙",
      "totalCount": 2478,
      "moduleCount": 5,
      "percentage": 45.2
    }
  ]
  ```

#### 新增Service方法
- `getOrderCountByModuleType(String startDate, String endDate)`: 获取按模块类型汇总的订单统计
- `getOrderCountByCity(String startDate, String endDate)`: 获取按地市汇总的订单统计

#### 新增Mapper方法
- `getOrderCountByModuleType(@Param("startDate") String startDate, @Param("endDate") String endDate)`: 按模块类型统计数据库查询
- `getOrderCountByCity(@Param("startDate") String startDate, @Param("endDate") String endDate)`: 按地市统计数据库查询

### 2. 前端功能

#### 新增API接口
- `getOrderCountByModuleType(data)`: 调用后端接口获取模块类型汇总数据
- `getOrderCountByCity(data)`: 调用后端接口获取地市汇总数据

#### 新增页面组件
在订单统计页面(`statistics.vue`)中新增了两个并排显示的汇总统计表格：

**左侧：按模块类型汇总统计表格**
- **数据字段**:
  - 序号
  - 模块类型名称
  - 类型编码
  - 订单总量（高亮显示）
  - 地市数
  - 占比（百分比，绿色显示）

**右侧：按地市汇总统计表格**
- **数据字段**:
  - 序号
  - 地市编码
  - 地市名称
  - 订单总量（高亮显示）
  - 模块数
  - 占比（百分比，绿色显示）

#### 新增样式
- `.total-count-highlight`: 订单总量高亮样式
- `.percentage-text`: 占比文字样式

## 数据库查询逻辑

**按模块类型汇总统计查询逻辑**：
1. `hnzsxh5_order_info` (订单表)
2. `hnzsxh5_goods_info` (商品表)
3. 通过商品表中的`MODULE_TYPE_NAME`和`MODULE_TYPE_CODE`字段进行模块类型分组统计

**按地市汇总统计查询逻辑**：
1. `hnzsxh5_order_info` (订单表)
2. `hnzsxh5_goods_info` (商品表)
3. 通过订单表中的`CITY_CODE`字段进行地市分组统计，统计每个地市的订单总量和涉及的模块数量

## 功能特点

1. **实时统计**: 支持按日期范围查询
2. **多维度展示**: 包含订单总量、地市覆盖数、模块数量、占比等维度
3. **可视化友好**: 重要数据高亮显示，占比用百分比形式展示
4. **响应式设计**: 表格支持响应式布局，两个表格并排显示
5. **数据完整性**: 对于未分类的模块类型显示为"未分类"
6. **优化排版**: 使用小尺寸表格，固定高度，提升页面美观度
7. **智能排序**: 按订单总量降序排列，便于快速识别重点业务

## 使用方式

1. 进入订单统计页面
2. 选择统计日期范围
3. 查看左侧"按模块类型汇总发展总量统计"表格
4. 查看右侧"按地市汇总发展总量统计"表格
5. 两个表格都按订单总量降序排列，方便查看主要业务模块和重点地市的发展情况
6. 可以通过占比数据快速了解各模块类型和地市的业务贡献度

## 技术实现

- **后端**: Spring Boot + MyBatis-Plus
- **前端**: Vue.js + Element UI
- **数据库**: MySQL
- **图表**: ECharts (预留扩展)

### 📈 使用效果

用户现在可以在订单统计页面看到：
1. 原有的各地市订单统计（饼图和趋势图）
2. **新增的按模块类型汇总发展总量统计表格（左侧）**
3. **新增的按地市汇总发展总量统计表格（右侧）**
4. 原有的各地市按模块分类订单数量统计表格

这些新功能可以帮助您：
- 快速了解各模块类型的业务发展情况
- 识别主要的业务增长点和重点地市
- 分析不同模块类型和地市的市场占有率
- 为业务决策提供数据支持

## 扩展建议

1. 可以考虑添加模块类型和地市的饼图或柱状图展示
2. 支持导出汇总统计数据
3. 添加模块类型和地市的趋势分析功能
4. 支持按地市筛选模块类型统计
5. 添加地市和模块类型的交叉分析功能
